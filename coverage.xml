<?xml version="1.0" ?>
<coverage version="7.9.2" timestamp="1751944900756" lines-valid="2101" lines-covered="987" line-rate="0.4698" branches-valid="332" branches-covered="103" branch-rate="0.3102" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.9.2 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/Users/<USER>/Library/CloudStorage/OneDrive-Personal/Documents/PythonOf/Codingagent/user-login-system/src</source>
	</sources>
	<packages>
		<package name="." line-rate="0.03366" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="enhanced_routes.py" filename="enhanced_routes.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="36" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="51" hits="0"/>
						<line number="56" hits="0"/>
						<line number="61" hits="0"/>
						<line number="63" hits="0"/>
						<line number="65" hits="0"/>
						<line number="70" hits="0"/>
						<line number="72" hits="0"/>
						<line number="84" hits="0"/>
						<line number="86" hits="0"/>
						<line number="87" hits="0"/>
						<line number="90" hits="0"/>
						<line number="91" hits="0"/>
						<line number="93" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="94,105"/>
						<line number="94" hits="0"/>
						<line number="99" hits="0"/>
						<line number="105" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="106,118"/>
						<line number="106" hits="0"/>
						<line number="111" hits="0"/>
						<line number="118" hits="0"/>
						<line number="124" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="138" hits="0"/>
						<line number="143" hits="0"/>
						<line number="144" hits="0"/>
						<line number="149" hits="0"/>
						<line number="155" hits="0"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="177" hits="0"/>
						<line number="180" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="181,189"/>
						<line number="181" hits="0"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="192" hits="0"/>
						<line number="200" hits="0"/>
						<line number="202" hits="0"/>
						<line number="205" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="206,212"/>
						<line number="206" hits="0"/>
						<line number="212" hits="0"/>
						<line number="213" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="214,220"/>
						<line number="214" hits="0"/>
						<line number="220" hits="0"/>
						<line number="222" hits="0"/>
						<line number="223" hits="0"/>
						<line number="231" hits="0"/>
						<line number="232" hits="0"/>
						<line number="234" hits="0"/>
						<line number="241" hits="0"/>
						<line number="248" hits="0"/>
						<line number="250" hits="0"/>
						<line number="252" hits="0"/>
						<line number="258" hits="0"/>
						<line number="260" hits="0"/>
						<line number="271" hits="0"/>
						<line number="273" hits="0"/>
						<line number="274" hits="0"/>
						<line number="275" hits="0"/>
						<line number="277" hits="0"/>
						<line number="278" hits="0"/>
						<line number="279" hits="0"/>
						<line number="281" hits="0"/>
						<line number="288" hits="0"/>
						<line number="290" hits="0"/>
						<line number="299" hits="0"/>
						<line number="305" hits="0"/>
						<line number="306" hits="0"/>
						<line number="319" hits="0"/>
						<line number="320" hits="0"/>
						<line number="321" hits="0"/>
						<line number="323" hits="0"/>
						<line number="330" hits="0"/>
						<line number="332" hits="0"/>
						<line number="334" hits="0"/>
						<line number="337" hits="0"/>
						<line number="345" hits="0"/>
						<line number="347" hits="0"/>
						<line number="353" hits="0"/>
						<line number="355" hits="0"/>
						<line number="357" hits="0"/>
						<line number="359" hits="0"/>
						<line number="367" hits="0"/>
						<line number="369" hits="0"/>
						<line number="371" hits="0"/>
						<line number="373" hits="0"/>
						<line number="380" hits="0"/>
						<line number="389" hits="0"/>
						<line number="395" hits="0"/>
						<line number="406" hits="0"/>
						<line number="412" hits="0"/>
						<line number="414" hits="0"/>
						<line number="421" hits="0"/>
						<line number="427" hits="0"/>
						<line number="432" hits="0"/>
						<line number="445" hits="0"/>
						<line number="448" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="449,456"/>
						<line number="449" hits="0"/>
						<line number="456" hits="0"/>
						<line number="458" hits="0"/>
						<line number="465" hits="0"/>
						<line number="467" hits="0"/>
						<line number="468" hits="0"/>
						<line number="470" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="472,515"/>
						<line number="472" hits="0"/>
						<line number="474" hits="0"/>
						<line number="480" hits="0"/>
						<line number="482" hits="0"/>
						<line number="484" hits="0"/>
						<line number="485" hits="0"/>
						<line number="498" hits="0"/>
						<line number="505" hits="0"/>
						<line number="512" hits="0"/>
						<line number="515" hits="0"/>
						<line number="522" hits="0"/>
						<line number="525" hits="0"/>
						<line number="530" hits="0"/>
						<line number="532" hits="0"/>
						<line number="539" hits="0"/>
						<line number="542" hits="0"/>
						<line number="544" hits="0"/>
						<line number="545" hits="0"/>
						<line number="552" hits="0"/>
						<line number="561" hits="0"/>
						<line number="566" hits="0"/>
						<line number="567" hits="0"/>
						<line number="580" hits="0"/>
						<line number="581" hits="0"/>
						<line number="583" hits="0"/>
						<line number="589" hits="0"/>
						<line number="591" hits="0"/>
						<line number="593" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="594,600"/>
						<line number="594" hits="0"/>
						<line number="600" hits="0"/>
						<line number="603" hits="0"/>
						<line number="604" hits="0"/>
						<line number="606" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="607,614"/>
						<line number="607" hits="0"/>
						<line number="614" hits="0"/>
						<line number="616" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="617,623"/>
						<line number="617" hits="0"/>
						<line number="623" hits="0"/>
						<line number="625" hits="0"/>
						<line number="626" hits="0"/>
						<line number="632" hits="0"/>
						<line number="639" hits="0"/>
						<line number="641" hits="0"/>
						<line number="646" hits="0"/>
						<line number="647" hits="0"/>
						<line number="653" hits="0"/>
						<line number="655" hits="0"/>
						<line number="661" hits="0"/>
						<line number="662" hits="0"/>
						<line number="663" hits="0"/>
						<line number="665" hits="0"/>
						<line number="666" hits="0"/>
						<line number="672" hits="0"/>
						<line number="682" hits="0"/>
						<line number="687" hits="0"/>
						<line number="688" hits="0"/>
						<line number="699" hits="0"/>
						<line number="701" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="702,710"/>
						<line number="702" hits="0"/>
						<line number="710" hits="0"/>
						<line number="711" hits="0"/>
						<line number="716" hits="0"/>
						<line number="725" hits="0"/>
						<line number="732" hits="0"/>
						<line number="734" hits="0"/>
						<line number="741" hits="0"/>
						<line number="743" hits="0"/>
						<line number="749" hits="0"/>
						<line number="750" hits="0"/>
					</lines>
				</class>
				<class name="enterprise_test_suite.py" filename="enterprise_test_suite.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="33" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="39" hits="0"/>
						<line number="41" hits="0"/>
						<line number="50" hits="0"/>
						<line number="52" hits="0"/>
						<line number="54" hits="0"/>
						<line number="56" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="65" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="70" hits="0"/>
						<line number="76" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="87" hits="0"/>
						<line number="89" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="103" hits="0"/>
						<line number="107" hits="0"/>
						<line number="110" hits="0"/>
						<line number="114" hits="0"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="124" hits="0"/>
						<line number="126" hits="0"/>
						<line number="134" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,135"/>
						<line number="135" hits="0"/>
						<line number="141" hits="0"/>
						<line number="143" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="134,144"/>
						<line number="144" hits="0"/>
						<line number="145" hits="0"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="150" hits="0"/>
						<line number="152" hits="0"/>
						<line number="161" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,162"/>
						<line number="162" hits="0"/>
						<line number="167" hits="0"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="171" hits="0"/>
						<line number="172" hits="0"/>
						<line number="174" hits="0"/>
						<line number="175" hits="0"/>
						<line number="176" hits="0"/>
						<line number="178" hits="0"/>
						<line number="184" hits="0"/>
						<line number="188" hits="0"/>
						<line number="197" hits="0"/>
						<line number="198" hits="0"/>
						<line number="205" hits="0"/>
						<line number="206" hits="0"/>
						<line number="208" hits="0"/>
						<line number="209" hits="0"/>
						<line number="216" hits="0"/>
						<line number="221" hits="0"/>
						<line number="222" hits="0"/>
						<line number="223" hits="0"/>
						<line number="224" hits="0"/>
						<line number="227" hits="0"/>
						<line number="228" hits="0"/>
						<line number="233" hits="0"/>
						<line number="234" hits="0"/>
						<line number="235" hits="0"/>
						<line number="236" hits="0"/>
						<line number="238" hits="0"/>
						<line number="239" hits="0"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="245" hits="0"/>
						<line number="246" hits="0"/>
						<line number="253" hits="0"/>
						<line number="258" hits="0"/>
						<line number="259" hits="0"/>
						<line number="260" hits="0"/>
						<line number="262" hits="0"/>
						<line number="263" hits="0"/>
						<line number="265" hits="0"/>
						<line number="273" hits="0"/>
						<line number="274" hits="0"/>
						<line number="275" hits="0"/>
						<line number="277" hits="0"/>
						<line number="278" hits="0"/>
						<line number="281" hits="0"/>
						<line number="286" hits="0"/>
						<line number="291" hits="0"/>
						<line number="292" hits="0"/>
						<line number="293" hits="0"/>
						<line number="296" hits="0"/>
						<line number="305" hits="0"/>
						<line number="306" hits="0"/>
						<line number="312" hits="0"/>
						<line number="314" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,315"/>
						<line number="315" hits="0"/>
						<line number="321" hits="0"/>
						<line number="324" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="314,325"/>
						<line number="325" hits="0"/>
						<line number="326" hits="0"/>
						<line number="328" hits="0"/>
						<line number="329" hits="0"/>
						<line number="331" hits="0"/>
						<line number="332" hits="0"/>
						<line number="338" hits="0"/>
						<line number="340" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,342"/>
						<line number="342" hits="0"/>
						<line number="348" hits="0"/>
						<line number="349" hits="0"/>
						<line number="350" hits="0"/>
						<line number="351" hits="0"/>
						<line number="353" hits="0"/>
						<line number="354" hits="0"/>
						<line number="361" hits="0"/>
						<line number="362" hits="0"/>
						<line number="364" hits="0"/>
						<line number="365" hits="0"/>
						<line number="372" hits="0"/>
						<line number="373" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="374,386"/>
						<line number="374" hits="0"/>
						<line number="379" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="380,381"/>
						<line number="380" hits="0"/>
						<line number="381" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="373,382"/>
						<line number="382" hits="0"/>
						<line number="386" hits="0"/>
						<line number="388" hits="0"/>
						<line number="389" hits="0"/>
						<line number="391" hits="0"/>
						<line number="398" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,399"/>
						<line number="399" hits="0"/>
						<line number="402" hits="0"/>
						<line number="403" hits="0"/>
						<line number="404" hits="0"/>
						<line number="407" hits="0"/>
						<line number="416" hits="0"/>
						<line number="417" hits="0"/>
						<line number="423" hits="0"/>
						<line number="426" hits="0"/>
						<line number="427" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="428,437"/>
						<line number="428" hits="0"/>
						<line number="429" hits="0"/>
						<line number="431" hits="0"/>
						<line number="435" hits="0"/>
						<line number="437" hits="0"/>
						<line number="438" hits="0"/>
						<line number="441" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="442,445"/>
						<line number="442" hits="0"/>
						<line number="445" hits="0"/>
						<line number="446" hits="0"/>
						<line number="447" hits="0"/>
						<line number="449" hits="0"/>
						<line number="457" hits="0"/>
						<line number="458" hits="0"/>
						<line number="465" hits="0"/>
						<line number="467" hits="0"/>
						<line number="468" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="469,480"/>
						<line number="469" hits="0"/>
						<line number="470" hits="0"/>
						<line number="472" hits="0"/>
						<line number="477" hits="0"/>
						<line number="480" hits="0"/>
						<line number="481" hits="0"/>
						<line number="482" hits="0"/>
						<line number="485" hits="0"/>
						<line number="486" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="487,493"/>
						<line number="487" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="488,490"/>
						<line number="488" hits="0"/>
						<line number="490" hits="0"/>
						<line number="493" hits="0"/>
						<line number="494" hits="0"/>
						<line number="496" hits="0"/>
						<line number="497" hits="0"/>
						<line number="503" hits="0"/>
						<line number="505" hits="0"/>
						<line number="506" hits="0"/>
						<line number="509" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="510,530"/>
						<line number="510" hits="0"/>
						<line number="511" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="512,521"/>
						<line number="512" hits="0"/>
						<line number="513" hits="0"/>
						<line number="515" hits="0"/>
						<line number="519" hits="0"/>
						<line number="521" hits="0"/>
						<line number="524" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="525,528"/>
						<line number="525" hits="0"/>
						<line number="528" hits="0"/>
						<line number="530" hits="0"/>
						<line number="531" hits="0"/>
						<line number="532" hits="0"/>
						<line number="535" hits="0"/>
						<line number="536" hits="0"/>
						<line number="540" hits="0"/>
						<line number="549" hits="0"/>
						<line number="553" hits="0"/>
						<line number="554" hits="0"/>
						<line number="555" hits="0"/>
						<line number="563" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="564,566"/>
						<line number="564" hits="0"/>
						<line number="566" hits="0"/>
						<line number="572" hits="0"/>
						<line number="574" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,576"/>
						<line number="576" hits="0"/>
						<line number="577" hits="0"/>
						<line number="579" hits="0"/>
						<line number="584" hits="0"/>
						<line number="585" hits="0"/>
						<line number="586" hits="0"/>
						<line number="588" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="589,592"/>
						<line number="589" hits="0"/>
						<line number="592" hits="0"/>
						<line number="593" hits="0"/>
						<line number="596" hits="0"/>
						<line number="599" hits="0"/>
						<line number="600" hits="0"/>
						<line number="603" hits="0"/>
						<line number="606" hits="0"/>
						<line number="615" hits="0"/>
						<line number="616" hits="0"/>
						<line number="623" hits="0"/>
						<line number="624" hits="0"/>
						<line number="625" hits="0"/>
						<line number="628" hits="0"/>
						<line number="632" hits="0"/>
						<line number="633" hits="0"/>
						<line number="636" hits="0"/>
						<line number="640" hits="0"/>
						<line number="641" hits="0"/>
						<line number="644" hits="0"/>
						<line number="648" hits="0"/>
						<line number="651" hits="0"/>
						<line number="655" hits="0"/>
						<line number="658" hits="0"/>
						<line number="662" hits="0"/>
						<line number="667" hits="0"/>
						<line number="671" hits="0"/>
						<line number="674" hits="0"/>
						<line number="678" hits="0"/>
						<line number="681" hits="0"/>
						<line number="685" hits="0"/>
						<line number="687" hits="0"/>
						<line number="688" hits="0"/>
						<line number="690" hits="0"/>
						<line number="692" hits="0"/>
						<line number="707" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,708"/>
						<line number="708" hits="0"/>
						<line number="714" hits="0"/>
						<line number="722" hits="0"/>
						<line number="726" hits="0"/>
						<line number="727" hits="0"/>
						<line number="729" hits="0"/>
						<line number="732" hits="0"/>
						<line number="733" hits="0"/>
						<line number="735" hits="0"/>
						<line number="739" hits="0"/>
						<line number="745" hits="0"/>
						<line number="748" hits="0"/>
						<line number="749" hits="0"/>
						<line number="751" hits="0"/>
						<line number="752" hits="0"/>
						<line number="753" hits="0"/>
						<line number="755" hits="0"/>
					</lines>
				</class>
				<class name="main.py" filename="main.py" complexity="0" line-rate="0.7273" branch-rate="1">
					<methods/>
					<lines>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="18" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="36" hits="0"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="42" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="58" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="69" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="73" hits="0"/>
						<line number="75" hits="1"/>
						<line number="78" hits="1"/>
					</lines>
				</class>
				<class name="main_backup.py" filename="main_backup.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="32" hits="0"/>
						<line number="35" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="44" hits="0"/>
						<line number="47" hits="0"/>
						<line number="48" hits="0"/>
						<line number="50" hits="0"/>
						<line number="52" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="68" hits="0"/>
						<line number="70" hits="0"/>
						<line number="71" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="76" hits="0"/>
						<line number="84" hits="0"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="89,96"/>
						<line number="89" hits="0"/>
						<line number="93" hits="0"/>
						<line number="96" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="97,100"/>
						<line number="97" hits="0"/>
						<line number="100" hits="0"/>
						<line number="103" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="104,106"/>
						<line number="104" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="109" hits="0"/>
						<line number="115" hits="0"/>
						<line number="116" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="124" hits="0"/>
						<line number="126" hits="0"/>
						<line number="128" hits="0"/>
						<line number="133" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="138" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="139,150"/>
						<line number="139" hits="0"/>
						<line number="140" hits="0"/>
						<line number="142" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="138,143"/>
						<line number="143" hits="0"/>
						<line number="150" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="151,154"/>
						<line number="151" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="150,152"/>
						<line number="152" hits="0"/>
						<line number="154" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="155,158"/>
						<line number="155" hits="0"/>
						<line number="158" hits="0"/>
						<line number="160" hits="0"/>
						<line number="162" hits="0"/>
						<line number="167" hits="0"/>
						<line number="168" hits="0"/>
						<line number="174" hits="0"/>
						<line number="176" hits="0"/>
						<line number="177" hits="0"/>
						<line number="180" hits="0"/>
						<line number="181" hits="0"/>
						<line number="183" hits="0"/>
						<line number="185" hits="0"/>
						<line number="186" hits="0"/>
						<line number="190" hits="0"/>
						<line number="192" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="198" hits="0"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="209" hits="0"/>
						<line number="211" hits="0"/>
						<line number="212" hits="0"/>
						<line number="213" hits="0"/>
						<line number="216" hits="0"/>
						<line number="224" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="225,232"/>
						<line number="225" hits="0"/>
						<line number="232" hits="0"/>
						<line number="234" hits="0"/>
						<line number="235" hits="0"/>
						<line number="240" hits="0"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="254" hits="0"/>
						<line number="259" hits="0"/>
						<line number="260" hits="0"/>
						<line number="266" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="278" hits="0"/>
						<line number="279" hits="0"/>
						<line number="280" hits="0"/>
						<line number="283" hits="0"/>
						<line number="285" hits="0"/>
						<line number="293" hits="0"/>
						<line number="296" hits="0"/>
						<line number="306" hits="0"/>
						<line number="309" hits="0"/>
						<line number="312" hits="0"/>
						<line number="315" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="316,319"/>
						<line number="316" hits="0"/>
						<line number="319" hits="0"/>
						<line number="320" hits="0"/>
						<line number="321" hits="0"/>
						<line number="324" hits="0"/>
						<line number="325" hits="0"/>
						<line number="326" hits="0"/>
						<line number="328" hits="0"/>
						<line number="329" hits="0"/>
						<line number="330" hits="0"/>
						<line number="332" hits="0"/>
						<line number="334" hits="0"/>
						<line number="336" hits="0"/>
						<line number="346" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="347,354"/>
						<line number="347" hits="0"/>
						<line number="348" hits="0"/>
						<line number="354" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="355,364"/>
						<line number="355" hits="0"/>
						<line number="364" hits="0"/>
						<line number="365" hits="0"/>
						<line number="366" hits="0"/>
						<line number="369" hits="0"/>
						<line number="376" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="377,382"/>
						<line number="377" hits="0"/>
						<line number="382" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="383,385"/>
						<line number="383" hits="0"/>
						<line number="385" hits="0"/>
						<line number="388" hits="0"/>
						<line number="389" hits="0"/>
						<line number="390" hits="0"/>
						<line number="391" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="392,396"/>
						<line number="392" hits="0"/>
						<line number="393" hits="0"/>
						<line number="396" hits="0"/>
						<line number="398" hits="0"/>
						<line number="399" hits="0"/>
						<line number="401" hits="0"/>
						<line number="403" hits="0"/>
						<line number="406" hits="0"/>
						<line number="407" hits="0"/>
						<line number="411" hits="0"/>
						<line number="414" hits="0"/>
						<line number="415" hits="0"/>
						<line number="417" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,418"/>
						<line number="418" hits="0"/>
						<line number="421" hits="0"/>
						<line number="424" hits="0"/>
						<line number="425" hits="0"/>
						<line number="434" hits="0"/>
						<line number="435" hits="0"/>
						<line number="437" hits="0"/>
						<line number="439" hits="0"/>
						<line number="440" hits="0"/>
						<line number="441" hits="0"/>
						<line number="442" hits="0"/>
						<line number="443" hits="0"/>
						<line number="446" hits="0"/>
						<line number="449" hits="0"/>
						<line number="450" hits="0"/>
						<line number="452" hits="0"/>
						<line number="454" hits="0"/>
						<line number="463" hits="0"/>
						<line number="464" hits="0"/>
						<line number="466" hits="0"/>
						<line number="468" hits="0"/>
						<line number="488" hits="0"/>
						<line number="496" hits="0"/>
						<line number="499" hits="0"/>
						<line number="502" hits="0"/>
						<line number="513" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="519,521"/>
						<line number="519" hits="0"/>
						<line number="521" hits="0"/>
						<line number="528" hits="0"/>
						<line number="529" hits="0"/>
						<line number="530" hits="0"/>
						<line number="531" hits="0"/>
						<line number="532" hits="0"/>
						<line number="533" hits="0"/>
						<line number="538" hits="0"/>
						<line number="540" hits="0"/>
						<line number="546" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api" line-rate="0.4832" branch-rate="0.25" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="routes.py" filename="api/routes.py" complexity="0" line-rate="0.5083" branch-rate="0.25">
					<methods/>
					<lines>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="35" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="53" hits="1"/>
						<line number="58" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="86" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="95" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="96,111"/>
						<line number="96" hits="0"/>
						<line number="101" hits="0"/>
						<line number="111" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="112,128"/>
						<line number="112" hits="0"/>
						<line number="117" hits="0"/>
						<line number="128" hits="0"/>
						<line number="134" hits="0"/>
						<line number="142" hits="1"/>
						<line number="143" hits="0"/>
						<line number="148" hits="0"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="165" hits="1"/>
						<line number="177" hits="1"/>
						<line number="184" hits="1"/>
						<line number="198" hits="1"/>
						<line number="201" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="202" hits="1"/>
						<line number="215" hits="1"/>
						<line number="216" hits="1"/>
						<line number="218" hits="1"/>
						<line number="226" hits="1"/>
						<line number="228" hits="1"/>
						<line number="231" hits="1"/>
						<line number="232" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="233" hits="1"/>
						<line number="239" hits="1"/>
						<line number="241" hits="1"/>
						<line number="242" hits="1"/>
						<line number="250" hits="0"/>
						<line number="251" hits="0"/>
						<line number="253" hits="0"/>
						<line number="260" hits="0"/>
						<line number="268" hits="1"/>
						<line number="270" hits="0"/>
						<line number="271" hits="0"/>
						<line number="273" hits="1"/>
						<line number="275" hits="1"/>
						<line number="276" hits="1"/>
						<line number="278" hits="0"/>
						<line number="279" hits="1"/>
						<line number="281" hits="0"/>
						<line number="282" hits="0"/>
						<line number="283" hits="1"/>
						<line number="285" hits="0"/>
						<line number="286" hits="0"/>
						<line number="287" hits="1"/>
						<line number="289" hits="1"/>
						<line number="296" hits="1"/>
						<line number="298" hits="1"/>
						<line number="312" hits="1"/>
						<line number="318" hits="1"/>
						<line number="319" hits="1"/>
						<line number="332" hits="1"/>
						<line number="334" hits="1"/>
						<line number="341" hits="1"/>
						<line number="342" hits="1"/>
						<line number="345" hits="1"/>
						<line number="352" hits="0"/>
						<line number="354" hits="0"/>
						<line number="360" hits="0"/>
						<line number="362" hits="0"/>
						<line number="364" hits="0"/>
						<line number="366" hits="0"/>
						<line number="374" hits="0"/>
						<line number="376" hits="0"/>
						<line number="378" hits="0"/>
						<line number="380" hits="0"/>
						<line number="387" hits="0"/>
						<line number="401" hits="1"/>
						<line number="407" hits="1"/>
						<line number="418" hits="1"/>
						<line number="424" hits="1"/>
						<line number="426" hits="1"/>
						<line number="434" hits="1"/>
						<line number="440" hits="1"/>
						<line number="445" hits="1"/>
						<line number="458" hits="1"/>
						<line number="461" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="462"/>
						<line number="462" hits="0"/>
						<line number="469" hits="1"/>
						<line number="471" hits="1"/>
						<line number="478" hits="1"/>
						<line number="480" hits="1"/>
						<line number="481" hits="1"/>
						<line number="483" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="485,506"/>
						<line number="485" hits="0"/>
						<line number="487" hits="0"/>
						<line number="489" hits="0"/>
						<line number="494" hits="0"/>
						<line number="495" hits="0"/>
						<line number="498" hits="0"/>
						<line number="504" hits="0"/>
						<line number="506" hits="0"/>
						<line number="509" hits="0"/>
						<line number="511" hits="0"/>
						<line number="513" hits="0"/>
						<line number="520" hits="0"/>
						<line number="523" hits="0"/>
						<line number="525" hits="0"/>
						<line number="526" hits="0"/>
						<line number="533" hits="0"/>
						<line number="542" hits="1"/>
						<line number="547" hits="1"/>
						<line number="548" hits="1"/>
						<line number="561" hits="0"/>
						<line number="562" hits="0"/>
						<line number="564" hits="0"/>
						<line number="570" hits="0"/>
						<line number="572" hits="0"/>
						<line number="574" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="575,581"/>
						<line number="575" hits="0"/>
						<line number="581" hits="0"/>
						<line number="584" hits="0"/>
						<line number="585" hits="0"/>
						<line number="587" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="588,595"/>
						<line number="588" hits="0"/>
						<line number="595" hits="0"/>
						<line number="597" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="598,604"/>
						<line number="598" hits="0"/>
						<line number="604" hits="0"/>
						<line number="605" hits="0"/>
						<line number="611" hits="0"/>
						<line number="618" hits="0"/>
						<line number="620" hits="0"/>
						<line number="625" hits="0"/>
						<line number="626" hits="0"/>
						<line number="632" hits="0"/>
						<line number="634" hits="0"/>
						<line number="640" hits="0"/>
						<line number="641" hits="0"/>
						<line number="647" hits="0"/>
						<line number="657" hits="1"/>
						<line number="662" hits="1"/>
						<line number="663" hits="1"/>
						<line number="674" hits="0"/>
						<line number="676" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="677,685"/>
						<line number="677" hits="0"/>
						<line number="685" hits="0"/>
						<line number="686" hits="0"/>
						<line number="691" hits="0"/>
						<line number="700" hits="0"/>
						<line number="707" hits="0"/>
						<line number="709" hits="0"/>
						<line number="716" hits="1"/>
						<line number="718" hits="0"/>
						<line number="724" hits="0"/>
						<line number="725" hits="0"/>
					</lines>
				</class>
				<class name="routes_legacy.py" filename="api/routes_legacy.py" complexity="0" line-rate="0" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="0"/>
						<line number="2" hits="0"/>
						<line number="4" hits="0"/>
						<line number="5" hits="0"/>
						<line number="7" hits="0"/>
						<line number="13" hits="0"/>
						<line number="14" hits="0"/>
						<line number="15" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="19" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="26" hits="0"/>
						<line number="28" hits="0"/>
						<line number="31" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="44" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
					</lines>
				</class>
				<class name="schemas.py" filename="api/schemas.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="application" line-rate="0.9172" branch-rate="0.8333" complexity="0">
			<classes>
				<class name="__init__.py" filename="application/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="auth_service.py" filename="application/auth_service.py" complexity="0" line-rate="0.8879" branch-rate="0.8077">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="47" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="62" hits="1"/>
						<line number="64" hits="1"/>
						<line number="67" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="77" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="95" hits="1"/>
						<line number="97" hits="1"/>
						<line number="100" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="1"/>
						<line number="109" hits="1"/>
						<line number="111" hits="1"/>
						<line number="113" hits="1"/>
						<line number="115" hits="1"/>
						<line number="117" hits="1"/>
						<line number="119" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="120"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="133" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="151" hits="1"/>
						<line number="153" hits="1"/>
						<line number="155" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="156" hits="1"/>
						<line number="161" hits="1"/>
						<line number="168" hits="1"/>
						<line number="169" hits="1"/>
						<line number="171" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="172" hits="1"/>
						<line number="177" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="184" hits="1"/>
						<line number="185" hits="1"/>
						<line number="190" hits="1"/>
						<line number="192" hits="1"/>
						<line number="194" hits="1"/>
						<line number="200" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="206"/>
						<line number="201" hits="1"/>
						<line number="206" hits="0"/>
						<line number="207" hits="0"/>
						<line number="211" hits="1"/>
						<line number="212" hits="0"/>
						<line number="216" hits="1"/>
						<line number="217" hits="1"/>
						<line number="221" hits="1"/>
						<line number="222" hits="1"/>
						<line number="227" hits="1"/>
						<line number="229" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="230" hits="1"/>
						<line number="233" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="234"/>
						<line number="234" hits="0"/>
						<line number="235" hits="0"/>
						<line number="245" hits="1"/>
						<line number="247" hits="1"/>
						<line number="249" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="252"/>
						<line number="250" hits="1"/>
						<line number="252" hits="1"/>
						<line number="253" hits="1"/>
						<line number="254" hits="1"/>
						<line number="257" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="258"/>
						<line number="258" hits="0"/>
						<line number="259" hits="0"/>
						<line number="265" hits="1"/>
						<line number="267" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="268" hits="1"/>
						<line number="270" hits="1"/>
						<line number="272" hits="0"/>
						<line number="274" hits="1"/>
						<line number="276" hits="0"/>
					</lines>
				</class>
				<class name="user_service.py" filename="application/user_service.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="61" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="67" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="78" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="config" line-rate="0.7844" branch-rate="0.3784" complexity="0">
			<classes>
				<class name="__init__.py" filename="config/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="settings.py" filename="config/settings.py" complexity="0" line-rate="0.7844" branch-rate="0.3784">
					<methods/>
					<lines>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="57" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="63" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="109"/>
						<line number="109" hits="0"/>
						<line number="110" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="115" hits="1"/>
						<line number="117" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="118"/>
						<line number="118" hits="0"/>
						<line number="121" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="122" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="123"/>
						<line number="123" hits="0"/>
						<line number="125" hits="1"/>
						<line number="127" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="151" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="160" hits="1"/>
						<line number="164" hits="1"/>
						<line number="165" hits="1"/>
						<line number="167" hits="1"/>
						<line number="168" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="169"/>
						<line number="169" hits="0"/>
						<line number="170" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="171"/>
						<line number="171" hits="0"/>
						<line number="172" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="177" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="178,180"/>
						<line number="178" hits="0"/>
						<line number="180" hits="0"/>
						<line number="181" hits="0"/>
						<line number="183" hits="1"/>
						<line number="184" hits="1"/>
						<line number="186" hits="0"/>
						<line number="188" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="203" hits="1"/>
						<line number="204" hits="1"/>
						<line number="205" hits="1"/>
						<line number="206" hits="1"/>
						<line number="209" hits="1"/>
						<line number="210" hits="1"/>
						<line number="211" hits="1"/>
						<line number="212" hits="1"/>
						<line number="215" hits="1"/>
						<line number="216" hits="1"/>
						<line number="218" hits="1"/>
						<line number="222" hits="1"/>
						<line number="223" hits="1"/>
						<line number="225" hits="0"/>
						<line number="226" hits="0"/>
						<line number="228" hits="1"/>
						<line number="238" hits="1"/>
						<line number="239" hits="1"/>
						<line number="240" hits="1"/>
						<line number="244" hits="1"/>
						<line number="248" hits="1"/>
						<line number="249" hits="1"/>
						<line number="252" hits="1"/>
						<line number="256" hits="1"/>
						<line number="257" hits="1"/>
						<line number="260" hits="1"/>
						<line number="261" hits="1"/>
						<line number="264" hits="1"/>
						<line number="266" hits="1"/>
						<line number="270" hits="1"/>
						<line number="271" hits="1"/>
						<line number="273" hits="1"/>
						<line number="274" hits="1"/>
						<line number="276" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="277"/>
						<line number="277" hits="0"/>
						<line number="278" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="279"/>
						<line number="279" hits="0"/>
						<line number="281" hits="1"/>
						<line number="283" hits="1"/>
						<line number="293" hits="1"/>
						<line number="294" hits="1"/>
						<line number="295" hits="1"/>
						<line number="296" hits="1"/>
						<line number="299" hits="1"/>
						<line number="300" hits="1"/>
						<line number="303" hits="1"/>
						<line number="304" hits="1"/>
						<line number="306" hits="1"/>
						<line number="310" hits="1"/>
						<line number="311" hits="1"/>
						<line number="313" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="314"/>
						<line number="314" hits="0"/>
						<line number="315" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="316"/>
						<line number="316" hits="0"/>
						<line number="318" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="319"/>
						<line number="319" hits="0"/>
						<line number="320" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="321"/>
						<line number="321" hits="0"/>
						<line number="323" hits="1"/>
						<line number="325" hits="1"/>
						<line number="335" hits="1"/>
						<line number="336" hits="1"/>
						<line number="337" hits="1"/>
						<line number="338" hits="1"/>
						<line number="339" hits="1"/>
						<line number="342" hits="1"/>
						<line number="343" hits="1"/>
						<line number="344" hits="1"/>
						<line number="345" hits="1"/>
						<line number="348" hits="1"/>
						<line number="349" hits="1"/>
						<line number="350" hits="1"/>
						<line number="353" hits="1"/>
						<line number="354" hits="1"/>
						<line number="355" hits="1"/>
						<line number="358" hits="1"/>
						<line number="359" hits="1"/>
						<line number="360" hits="1"/>
						<line number="363" hits="1"/>
						<line number="364" hits="1"/>
						<line number="366" hits="1"/>
						<line number="370" hits="1"/>
						<line number="371" hits="1"/>
						<line number="373" hits="1"/>
						<line number="375" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="376"/>
						<line number="376" hits="0"/>
						<line number="378" hits="1"/>
						<line number="380" hits="1"/>
						<line number="390" hits="1"/>
						<line number="391" hits="1"/>
						<line number="392" hits="1"/>
						<line number="393" hits="1"/>
						<line number="396" hits="1"/>
						<line number="397" hits="1"/>
						<line number="398" hits="1"/>
						<line number="399" hits="1"/>
						<line number="402" hits="1"/>
						<line number="403" hits="1"/>
						<line number="404" hits="1"/>
						<line number="407" hits="1"/>
						<line number="408" hits="1"/>
						<line number="409" hits="1"/>
						<line number="410" hits="1"/>
						<line number="413" hits="1"/>
						<line number="414" hits="1"/>
						<line number="415" hits="1"/>
						<line number="416" hits="1"/>
						<line number="417" hits="1"/>
						<line number="418" hits="1"/>
						<line number="420" hits="1"/>
						<line number="427" hits="1"/>
						<line number="428" hits="1"/>
						<line number="429" hits="1"/>
						<line number="430" hits="1"/>
						<line number="431" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="432"/>
						<line number="432" hits="0"/>
						<line number="437" hits="1"/>
						<line number="439" hits="1"/>
						<line number="440" hits="1"/>
						<line number="441" hits="1"/>
						<line number="443" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="444"/>
						<line number="444" hits="0"/>
						<line number="445" hits="1"/>
						<line number="447" hits="1"/>
						<line number="448" hits="1"/>
						<line number="449" hits="1"/>
						<line number="450" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="451"/>
						<line number="451" hits="0"/>
						<line number="452" hits="1"/>
						<line number="455" hits="1"/>
						<line number="456" hits="1"/>
						<line number="460" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="467"/>
						<line number="461" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="462"/>
						<line number="462" hits="0"/>
						<line number="463" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="464"/>
						<line number="464" hits="0"/>
						<line number="467" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="472"/>
						<line number="468" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="469"/>
						<line number="469" hits="0"/>
						<line number="472" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="473"/>
						<line number="473" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="474,476"/>
						<line number="474" hits="0"/>
						<line number="476" hits="1"/>
						<line number="478" hits="1"/>
						<line number="480" hits="0"/>
						<line number="482" hits="1"/>
						<line number="484" hits="0"/>
						<line number="486" hits="1"/>
						<line number="488" hits="1"/>
						<line number="490" hits="1"/>
						<line number="492" hits="1"/>
						<line number="494" hits="1"/>
						<line number="496" hits="0"/>
						<line number="499" hits="1"/>
						<line number="510" hits="1"/>
						<line number="511" hits="1"/>
						<line number="513" hits="1"/>
						<line number="514" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="516"/>
						<line number="515" hits="1"/>
						<line number="516" hits="1"/>
						<line number="518" hits="1"/>
						<line number="520" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="539"/>
						<line number="521" hits="1"/>
						<line number="523" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="524"/>
						<line number="524" hits="0"/>
						<line number="526" hits="1"/>
						<line number="528" hits="1"/>
						<line number="535" hits="0"/>
						<line number="536" hits="0"/>
						<line number="537" hits="0"/>
						<line number="539" hits="1"/>
						<line number="541" hits="1"/>
						<line number="543" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="544" hits="1"/>
						<line number="545" hits="1"/>
						<line number="547" hits="1"/>
						<line number="549" hits="0"/>
						<line number="550" hits="0"/>
						<line number="552" hits="1"/>
						<line number="554" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="555,558"/>
						<line number="555" hits="0"/>
						<line number="556" hits="0"/>
						<line number="558" hits="0"/>
						<line number="561" hits="1"/>
						<line number="563" hits="1"/>
						<line number="565" hits="1"/>
						<line number="568" hits="1"/>
						<line number="570" hits="0"/>
						<line number="571" hits="0"/>
						<line number="574" hits="1"/>
						<line number="582" hits="0"/>
						<line number="583" hits="0"/>
						<line number="592" hits="0"/>
						<line number="593" hits="0"/>
						<line number="594" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="595,600"/>
						<line number="595" hits="0"/>
						<line number="596" hits="0"/>
						<line number="597" hits="0"/>
						<line number="600" hits="0"/>
						<line number="601" hits="0"/>
						<line number="602" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="603,608"/>
						<line number="603" hits="0"/>
						<line number="604" hits="0"/>
						<line number="605" hits="0"/>
						<line number="608" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="609,619"/>
						<line number="609" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="610,612"/>
						<line number="610" hits="0"/>
						<line number="612" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="613,615"/>
						<line number="613" hits="0"/>
						<line number="615" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="616,619"/>
						<line number="616" hits="0"/>
						<line number="619" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="620,621"/>
						<line number="620" hits="0"/>
						<line number="621" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="622,624"/>
						<line number="622" hits="0"/>
						<line number="624" hits="0"/>
						<line number="627" hits="1"/>
						<line number="629" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="domain" line-rate="1" branch-rate="0.875" complexity="0">
			<classes>
				<class name="__init__.py" filename="domain/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="auth.py" filename="domain/auth.py" complexity="0" line-rate="1" branch-rate="0.875">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="35" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="41" hits="1"/>
						<line number="43" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="60"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="58" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
					</lines>
				</class>
				<class name="user.py" filename="domain/user.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="infrastructure" line-rate="0.8148" branch-rate="1" complexity="0">
			<classes>
				<class name="__init__.py" filename="infrastructure/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="db.py" filename="infrastructure/db.py" complexity="0" line-rate="0.8182" branch-rate="1">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
					</lines>
				</class>
				<class name="email_service.py" filename="infrastructure/email_service.py" complexity="0" line-rate="0.814" branch-rate="1">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="30" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="63" hits="1"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="69" hits="1"/>
						<line number="70" hits="0"/>
						<line number="71" hits="0"/>
						<line number="75" hits="1"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="shared" line-rate="0.8706" branch-rate="0.5" complexity="0">
			<classes>
				<class name="__init__.py" filename="shared/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="security.py" filename="shared/security.py" complexity="0" line-rate="0.7556" branch-rate="0">
					<methods/>
					<lines>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="0"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="32,33"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="34,35"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="36,37"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="38,39"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="41" hits="1"/>
						<line number="43" hits="0"/>
						<line number="45" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="51" hits="1"/>
						<line number="53" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
					</lines>
				</class>
				<class name="utils.py" filename="shared/utils.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="28" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="41" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="42" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="1"/>
						<line number="54" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="55" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="62" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="shared.errors" line-rate="0.765" branch-rate="0.5" complexity="0">
			<classes>
				<class name="__init__.py" filename="shared/errors/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="exceptions.py" filename="shared/errors/exceptions.py" complexity="0" line-rate="0.765" branch-rate="0.5">
					<methods/>
					<lines>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1"/>
						<line number="76" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="102" hits="1"/>
						<line number="110" hits="1"/>
						<line number="112" hits="1"/>
						<line number="114" hits="1"/>
						<line number="125" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="126" hits="1"/>
						<line number="128" hits="1"/>
						<line number="130" hits="1"/>
						<line number="133" hits="1"/>
						<line number="139" hits="0"/>
						<line number="147" hits="0"/>
						<line number="149" hits="1"/>
						<line number="152" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="1"/>
						<line number="167" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="170"/>
						<line number="168" hits="1"/>
						<line number="170" hits="1"/>
						<line number="178" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="183" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="184" hits="1"/>
						<line number="186" hits="1"/>
						<line number="188" hits="1"/>
						<line number="190" hits="1"/>
						<line number="193" hits="1"/>
						<line number="201" hits="1"/>
						<line number="210" hits="1"/>
						<line number="211" hits="1"/>
						<line number="213" hits="1"/>
						<line number="216" hits="1"/>
						<line number="222" hits="1"/>
						<line number="230" hits="1"/>
						<line number="233" hits="1"/>
						<line number="234" hits="1"/>
						<line number="236" hits="1"/>
						<line number="237" hits="1"/>
						<line number="238" hits="1"/>
						<line number="240" hits="1"/>
						<line number="242" hits="1"/>
						<line number="243" hits="1"/>
						<line number="244" hits="1"/>
						<line number="246" hits="1"/>
						<line number="255" hits="1"/>
						<line number="256" hits="1"/>
						<line number="257" hits="1"/>
						<line number="258" hits="1"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1"/>
						<line number="261" hits="1"/>
						<line number="263" hits="1"/>
						<line number="265" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="266"/>
						<line number="266" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="267,271"/>
						<line number="267" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="271" hits="0"/>
						<line number="277" hits="1"/>
						<line number="278" hits="1"/>
						<line number="279" hits="0"/>
						<line number="280" hits="0"/>
						<line number="281" hits="0"/>
						<line number="282" hits="0"/>
						<line number="283" hits="0"/>
						<line number="290" hits="1"/>
						<line number="292" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="293,294"/>
						<line number="293" hits="0"/>
						<line number="294" hits="0"/>
						<line number="296" hits="1"/>
						<line number="298" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="299"/>
						<line number="299" hits="0"/>
						<line number="300" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="exit,301"/>
						<line number="301" hits="0"/>
						<line number="302" hits="0"/>
						<line number="303" hits="0"/>
						<line number="305" hits="1"/>
						<line number="307" hits="1"/>
						<line number="309" hits="1"/>
						<line number="310" hits="1"/>
						<line number="312" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="314"/>
						<line number="314" hits="0"/>
						<line number="315" hits="0"/>
						<line number="316" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="317"/>
						<line number="317" hits="0"/>
						<line number="319" hits="1"/>
						<line number="321" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="322"/>
						<line number="322" hits="0"/>
						<line number="323" hits="0"/>
						<line number="324" hits="1"/>
						<line number="327" hits="1"/>
						<line number="328" hits="1"/>
						<line number="330" hits="1"/>
						<line number="331" hits="1"/>
						<line number="332" hits="1"/>
						<line number="333" hits="1"/>
						<line number="334" hits="1"/>
						<line number="336" hits="1"/>
						<line number="350" hits="1"/>
						<line number="352" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="394"/>
						<line number="353" hits="1"/>
						<line number="354" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="355"/>
						<line number="355" hits="0"/>
						<line number="360" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="361,364"/>
						<line number="361" hits="0"/>
						<line number="362" hits="0"/>
						<line number="364" hits="0"/>
						<line number="371" hits="1"/>
						<line number="373" hits="1"/>
						<line number="375" hits="0"/>
						<line number="376" hits="0"/>
						<line number="378" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="379,387"/>
						<line number="379" hits="0"/>
						<line number="385" hits="0"/>
						<line number="387" hits="0"/>
						<line number="394" hits="0"/>
						<line number="397" hits="1"/>
						<line number="411" hits="1"/>
						<line number="412" hits="1"/>
						<line number="413" hits="1"/>
						<line number="415" hits="1"/>
						<line number="421" hits="1"/>
						<line number="422" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="423" hits="1"/>
						<line number="425" hits="1"/>
						<line number="427" hits="1"/>
						<line number="428" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="435"/>
						<line number="429" hits="1"/>
						<line number="435" hits="0"/>
						<line number="437" hits="0"/>
						<line number="439" hits="0"/>
						<line number="440" hits="0"/>
						<line number="442" hits="0"/>
						<line number="448" hits="0"/>
						<line number="456" hits="1"/>
						<line number="457" hits="1"/>
						<line number="460" hits="1"/>
						<line number="469" hits="1"/>
						<line number="472" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="473" hits="1"/>
						<line number="474" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="475"/>
						<line number="475" hits="0"/>
						<line number="476" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="477" hits="1"/>
						<line number="479" hits="1"/>
						<line number="481" hits="1"/>
						<line number="498" hits="1"/>
						<line number="500" hits="0"/>
						<line number="502" hits="0"/>
						<line number="511" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="shared.observability" line-rate="0.4571" branch-rate="0.15" complexity="0">
			<classes>
				<class name="__init__.py" filename="shared/observability/__init__.py" complexity="0" line-rate="1" branch-rate="1">
					<methods/>
					<lines/>
				</class>
				<class name="monitoring.py" filename="shared/observability/monitoring.py" complexity="0" line-rate="0.4571" branch-rate="0.15">
					<methods/>
					<lines>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="34" hits="1"/>
						<line number="36" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="58" hits="1"/>
						<line number="64" hits="1"/>
						<line number="69" hits="1"/>
						<line number="75" hits="1"/>
						<line number="81" hits="1"/>
						<line number="87" hits="1"/>
						<line number="92" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="117" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="131" hits="1"/>
						<line number="139" hits="1"/>
						<line number="146" hits="0"/>
						<line number="148" hits="1"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="152,158"/>
						<line number="152" hits="0"/>
						<line number="158" hits="0"/>
						<line number="160" hits="0"/>
						<line number="162" hits="0"/>
						<line number="167" hits="0"/>
						<line number="169" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="170,175"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="172" hits="0"/>
						<line number="175" hits="0"/>
						<line number="176" hits="0"/>
						<line number="178" hits="0"/>
						<line number="185" hits="0"/>
						<line number="186" hits="0"/>
						<line number="187" hits="0"/>
						<line number="193" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="203" hits="0"/>
						<line number="205" hits="1"/>
						<line number="207" hits="0"/>
						<line number="211" hits="0"/>
						<line number="213" hits="0"/>
						<line number="214" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="215,226"/>
						<line number="215" hits="0"/>
						<line number="217" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="218,224"/>
						<line number="218" hits="0"/>
						<line number="224" hits="0"/>
						<line number="226" hits="0"/>
						<line number="227" hits="0"/>
						<line number="229" hits="1"/>
						<line number="231" hits="0"/>
						<line number="232" hits="0"/>
						<line number="234" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="235,242"/>
						<line number="235" hits="0"/>
						<line number="237" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="238,239"/>
						<line number="238" hits="0"/>
						<line number="239" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="234,240"/>
						<line number="240" hits="0"/>
						<line number="242" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="243,244"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="245,247"/>
						<line number="245" hits="0"/>
						<line number="247" hits="0"/>
						<line number="250" hits="1"/>
						<line number="252" hits="0"/>
						<line number="253" hits="0"/>
						<line number="255" hits="0"/>
						<line number="257" hits="0"/>
						<line number="258" hits="0"/>
						<line number="261" hits="0"/>
						<line number="262" hits="0"/>
						<line number="264" hits="0"/>
						<line number="267" hits="0"/>
						<line number="269" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="270,277"/>
						<line number="270" hits="0"/>
						<line number="277" hits="0"/>
						<line number="284" hits="0"/>
						<line number="285" hits="0"/>
						<line number="292" hits="1"/>
						<line number="294" hits="0"/>
						<line number="295" hits="0"/>
						<line number="297" hits="0"/>
						<line number="300" hits="0"/>
						<line number="303" hits="0"/>
						<line number="304" hits="0"/>
						<line number="306" hits="0"/>
						<line number="307" hits="0"/>
						<line number="308" hits="0"/>
						<line number="310" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="311,313"/>
						<line number="311" hits="0"/>
						<line number="313" hits="0"/>
						<line number="315" hits="0"/>
						<line number="317" hits="0"/>
						<line number="324" hits="0"/>
						<line number="325" hits="0"/>
						<line number="332" hits="1"/>
						<line number="334" hits="0"/>
						<line number="335" hits="0"/>
						<line number="337" hits="0"/>
						<line number="340" hits="0"/>
						<line number="346" hits="0"/>
						<line number="347" hits="0"/>
						<line number="351" hits="0"/>
						<line number="353" hits="0"/>
						<line number="355" hits="0"/>
						<line number="362" hits="0"/>
						<line number="363" hits="0"/>
						<line number="370" hits="1"/>
						<line number="372" hits="0"/>
						<line number="374" hits="0"/>
						<line number="377" hits="0"/>
						<line number="378" hits="0"/>
						<line number="381" hits="0"/>
						<line number="382" hits="0"/>
						<line number="384" hits="0"/>
						<line number="392" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="393,395"/>
						<line number="393" hits="0"/>
						<line number="394" hits="0"/>
						<line number="395" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="396,399"/>
						<line number="396" hits="0"/>
						<line number="397" hits="0"/>
						<line number="399" hits="0"/>
						<line number="400" hits="0"/>
						<line number="402" hits="0"/>
						<line number="409" hits="0"/>
						<line number="410" hits="0"/>
						<line number="417" hits="1"/>
						<line number="426" hits="1"/>
						<line number="427" hits="1"/>
						<line number="428" hits="1"/>
						<line number="429" hits="1"/>
						<line number="430" hits="1"/>
						<line number="433" hits="1"/>
						<line number="440" hits="1"/>
						<line number="448" hits="1"/>
						<line number="454" hits="1"/>
						<line number="460" hits="1"/>
						<line number="461" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="462"/>
						<line number="462" hits="0"/>
						<line number="464" hits="1"/>
						<line number="466" hits="1"/>
						<line number="472" hits="1"/>
						<line number="474" hits="1"/>
						<line number="476" hits="1"/>
						<line number="477" hits="1"/>
						<line number="479" hits="1"/>
						<line number="481" hits="0"/>
						<line number="486" hits="1"/>
						<line number="488" hits="0"/>
						<line number="490" hits="1"/>
						<line number="492" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="493,502"/>
						<line number="493" hits="0"/>
						<line number="502" hits="0"/>
						<line number="503" hits="0"/>
						<line number="506" hits="0"/>
						<line number="507" hits="0"/>
						<line number="508" hits="0"/>
						<line number="510" hits="0"/>
						<line number="511" hits="0"/>
						<line number="512" hits="0"/>
						<line number="515" hits="0"/>
						<line number="516" hits="0"/>
						<line number="519" hits="0"/>
						<line number="520" hits="0"/>
						<line number="523" hits="0"/>
						<line number="524" hits="0"/>
						<line number="526" hits="0"/>
						<line number="536" hits="1"/>
						<line number="537" hits="1"/>
						<line number="540" hits="1"/>
						<line number="543" hits="1"/>
						<line number="544" hits="1"/>
						<line number="546" hits="1"/>
						<line number="547" hits="1" branch="true" condition-coverage="50% (1/2)" missing-branches="548"/>
						<line number="548" hits="0"/>
						<line number="549" hits="0"/>
						<line number="551" hits="1"/>
						<line number="553" hits="1"/>
						<line number="554" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="555" hits="1"/>
						<line number="556" hits="1"/>
						<line number="559" hits="1"/>
						<line number="560" hits="1"/>
						<line number="563" hits="1"/>
						<line number="566" hits="1"/>
						<line number="574" hits="1"/>
						<line number="576" hits="1"/>
						<line number="579" hits="1"/>
						<line number="591" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="592,601"/>
						<line number="592" hits="0"/>
						<line number="593" hits="0"/>
						<line number="601" hits="0"/>
						<line number="609" hits="0"/>
						<line number="610" hits="0"/>
						<line number="613" hits="0"/>
						<line number="615" hits="0"/>
						<line number="628" hits="0" branch="true" condition-coverage="0% (0/2)" missing-branches="629,641"/>
						<line number="629" hits="0"/>
						<line number="641" hits="0"/>
						<line number="643" hits="0"/>
						<line number="648" hits="1"/>
						<line number="650" hits="0"/>
						<line number="655" hits="1"/>
						<line number="663" hits="0"/>
						<line number="665" hits="0"/>
						<line number="667" hits="0"/>
						<line number="668" hits="0"/>
						<line number="670" hits="0"/>
						<line number="671" hits="0"/>
						<line number="675" hits="0"/>
						<line number="677" hits="0"/>
						<line number="679" hits="0"/>
						<line number="680" hits="0"/>
						<line number="681" hits="0"/>
						<line number="686" hits="1"/>
						<line number="694" hits="0"/>
						<line number="700" hits="1"/>
						<line number="708" hits="1"/>
						<line number="711" hits="1"/>
						<line number="712" hits="1"/>
						<line number="713" hits="1"/>
						<line number="714" hits="1"/>
						<line number="715" hits="1"/>
						<line number="718" hits="1"/>
						<line number="733" hits="1"/>
						<line number="741" hits="1"/>
						<line number="743" hits="1"/>
						<line number="744" hits="1"/>
						<line number="751" hits="1"/>
						<line number="753" hits="1"/>
						<line number="754" hits="1"/>
						<line number="756" hits="1" branch="true" condition-coverage="100% (2/2)"/>
						<line number="757" hits="1"/>
						<line number="759" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
